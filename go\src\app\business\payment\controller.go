package payment

import (
	"reflect"

	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

type Manager struct{}

func init() {
	manager := Manager{}
	gf.Register(&manager, reflect.TypeOf(manager).PkgPath())
}

// ProcessRefund 退款
func (m *Manager) ProcessRefund(ctx *gin.Context) {
	// 1. 参数验证
	var req RefundRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数格式错误", err.Error())
		return
	}

	// 2. 使用 JSON Schema 验证
	validator := jsonschema.NewValidator(GetRefundRequestSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"transaction_id": req.TransactionID,
		"refund_amount":  req.RefundAmount,
		"refund_reason":  req.RefundReason,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取当前操作员信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	// 4. 调用服务层处理退款
	paymentService := NewPaymentServiceWithOptions(
		ctx,
		WithRepository(),
		WithOrderModel(),
		WithBillModel(),
	)
	response, err := paymentService.ProcessRefund(&req, currentUserID, userClaims.Name)
	if err != nil {
		results.Failed(ctx, "退款处理失败", err.Error())
		return
	}

	results.Success(ctx, "请求成功", response, nil)
}

// RefreshRefundStatus 刷新退款状态
func (m *Manager) RefreshRefundStatus(ctx *gin.Context) {
	var req RefreshRefundStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数格式错误", err.Error())
		return
	}

	validator := jsonschema.NewValidator(GetRefreshRefundStatusRequestSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"transaction_id": req.TransactionID,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	paymentService := NewPaymentServiceWithOptions(
		ctx,
		WithRepository(),
		WithOrderModel(),
		WithBillModel(),
	)
	err := paymentService.QueryRefundStatus(req.TransactionID)
	if err != nil {
		results.Failed(ctx, "刷新退款状态失败", err.Error())
		return
	}

	results.Success(ctx, "刷新退款状态成功", nil, nil)
}

// ProcessPartialOfflinePayment 处理部分线下支付
func (m *Manager) ProcessPartialOfflinePayment(ctx *gin.Context) {
	// 1. 参数验证
	var req PartialOfflinePaymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数格式错误", err.Error())
		return
	}

	// 2. 使用 JSON Schema 验证
	validator := jsonschema.NewValidator(GetPartialOfflinePaymentRequestSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"bill_id":         req.BillID,
		"payment_channel": req.PaymentChannel,
		"voucher":         req.Voucher,
		"amount":          req.Amount,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取当前操作员信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}

	// 4. 调用服务层处理部分线下支付
	paymentService := NewPaymentServiceWithOptions(
		ctx,
		WithRepository(),
	)
	response, err := paymentService.ProcessPartialOfflinePayment(&req, currentUserID, userClaims.Name)
	if err != nil {
		results.Failed(ctx, "部分线下支付处理失败", err.Error())
		return
	}

	results.Success(ctx, "部分线下支付处理成功", response, nil)
}

// CalculateRepaymentSchedule 计算还款计划
func (m *Manager) CalculateRepaymentSchedule(ctx *gin.Context) {
	// 1. 参数验证
	var req CalculateScheduleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数格式错误", err.Error())
		return
	}

	// 2. 使用 JSON Schema 验证
	validator := jsonschema.NewValidator(GetCalculateScheduleRequestSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"loan_amount":          req.LoanAmount,
		"loan_period":          req.LoanPeriod,
		"total_periods":        req.TotalPeriods,
		"guarantee_fee":        req.GuaranteeFee,
		"annual_interest_rate": req.AnnualInterestRate,
		"other_fees":           req.OtherFees,
		"is_pre_payment":       req.IsPrePayment,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 调用服务层计算还款计划
	paymentService := NewPaymentServiceWithOptions(
		ctx,
		WithRepository(),
	)
	schedule, err := paymentService.CalculateRepaymentSchedule(&req)
	if err != nil {
		results.Failed(ctx, "计算还款计划失败", err.Error())
		return
	}

	results.Success(ctx, "计算还款计划成功", schedule, nil)
}

// CancelOfflinePayment 销账取消接口
func (m *Manager) CancelOfflinePayment(ctx *gin.Context) {
	// 1. 参数验证
	var req CancelOfflinePaymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数格式错误", err.Error())
		return
	}

	// 2. 使用 JSON Schema 验证
	validator := jsonschema.NewValidator(GetCancelOfflinePaymentRequestSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"transaction_no": req.TransactionNo,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取当前操作员信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}

	// 4. 调用服务层处理销账取消
	paymentService := NewPaymentServiceWithOptions(
		ctx,
		WithRepository(),
	)
	response, err := paymentService.ProcessCancelOfflinePayment(&req, currentUserID, userClaims.Name)
	if err != nil {
		results.Failed(ctx, "销账取消处理失败", err.Error())
		return
	}

	results.Success(ctx, "销账取消处理成功", response, nil)
}
