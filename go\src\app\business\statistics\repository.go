package statistics

import (
	"context"
	"fmt"
	"time"

	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/pagination"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
)

// Repository 渠道统计数据仓库层
type Repository struct {
	ctx context.Context
}

// NewRepository 创建渠道统计数据仓库实例
func NewRepository(ctx context.Context) *Repository {
	return &Repository{
		ctx: ctx,
	}
}

// GetEnabledChannels 获取所有启用的渠道
func (r *Repository) GetEnabledChannels() ([]gform.Data, error) {
	result, err := model.DB(model.WithContext(r.ctx)).Table("channel").
		Where("channel_status", 1).
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询启用渠道列表失败: %v", err)
	}

	return result, nil
}

// GetNewUserCountByChannel 统计渠道当天新用户数
func (r *Repository) GetNewUserCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	result, err := model.DB(model.WithContext(r.ctx)).Table("business_app_account").
		Where("channelId", "=", channelID).
		Where("createtime", ">=", startTimestamp).
		Where("createtime", "<", endTimestamp).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道新用户数失败: %v", err)
	}

	return uint(result), nil
}

// GetRealNameCountByChannel 统计渠道当天实名通过数
func (r *Repository) GetRealNameCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {

	result, err := model.DB(model.WithContext(r.ctx)).Table("business_app_account").
		Where("channelId", "=", channelID).
		// identityStatus 值为 2 表示通过
		Where("identityStatus", "=", 2).
		Where("identitySuccessTime", ">=", startTime).
		Where("identitySuccessTime", "<", endTime).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道实名通过数失败: %v", err)
	}

	return uint(result), nil
}

// GetTransactionCountByChannel 统计渠道当天成交数
func (r *Repository) GetTransactionCountByChannel(channelID uint, startTime, endTime time.Time) (uint, error) {
	// 统计成功放款的订单数
	result, err := model.DB(model.WithContext(r.ctx)).Table("business_loan_orders").
		Where("channel_id", "=", channelID).
		WhereIn("status", []interface{}{model.OrderStatusDisbursed, model.OrderStatusCompleted}).
		Where("disbursed_at", ">=", startTime).
		Where("disbursed_at", "<", endTime).
		Count()

	if err != nil {
		return 0, fmt.Errorf("统计渠道成交数失败: %v", err)
	}

	return uint(result), nil
}

// SaveChannelStatistics 保存渠道统计数据
func (r *Repository) SaveChannelStatistics(channelID uint, newUserCount, realNameCount, transactionCount uint, date carbon.Carbon) error {
	// 获取当前日期
	today := date.Format("Y-m-d")

	// 检查今天是否已有统计记录
	existing, err := model.DB(model.WithContext(r.ctx)).Table("channel_statistics").
		Where("channel_id", "=", channelID).
		Where("DATE(created_at)", "=", today).
		First()

	if err != nil {
		return fmt.Errorf("查询现有统计记录失败: %v", err)
	}

	data := map[string]interface{}{
		"channel_id":             channelID,
		"new_customer_reg_num":   newUserCount,
		"real_name_num":          realNameCount,
		"number_of_transactions": transactionCount,
		"created_at":             time.Now(),
	}

	if existing == nil {
		// 新增记录
		_, err = model.DB(model.WithContext(r.ctx)).Table("channel_statistics").Insert(data)
		if err != nil {
			return fmt.Errorf("插入渠道统计数据失败: %v", err)
		}
	} else {
		// 更新记录 - 移除created_at字段
		updateData := map[string]interface{}{
			"new_customer_reg_num":   newUserCount,
			"real_name_num":          realNameCount,
			"number_of_transactions": transactionCount,
			"updated_at":             time.Now(),
		}
		_, err = model.DB(model.WithContext(r.ctx)).Table("channel_statistics").
			Where("channel_id", "=", channelID).
			Where("DATE(created_at)", "=", today).
			Update(updateData)
		if err != nil {
			return fmt.Errorf("更新渠道统计数据失败: %v", err)
		}
	}

	return nil
}

// ChannelInfo 渠道信息结构体
type ChannelInfo struct {
	ID          uint   `json:"id"`           // 渠道IDs
	ChannelName string `json:"channel_name"` // 渠道名称
	ChannelCode string `json:"channel_code"` // 渠道编码
}

// ConvertToChannelInfo 将map数据转换为ChannelInfo结构体
func (r *Repository) ConvertToChannelInfo(data map[string]interface{}) (*ChannelInfo, error) {
	var channelInfo ChannelInfo
	err := gconv.Struct(data, &channelInfo)
	if err != nil {
		return nil, fmt.Errorf("转换渠道信息失败: %v", err)
	}
	return &channelInfo, nil
}

// GetChannelStatisticsListByParams 根据参数查询渠道统计列表
func (r *Repository) GetChannelStatisticsListByParams(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建基础查询条件
	countQuery := r.buildChannelStatisticsQuery(params)
	dataQuery := r.buildChannelStatisticsQuery(params).
		Fields("cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at").
		OrderBy("cs.created_at DESC")

	// 使用公共分页工具执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("查询渠道统计列表失败: %v", err)
	}

	return result, nil
}

// buildChannelStatisticsQuery 构建渠道统计查询条件
func (r *Repository) buildChannelStatisticsQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB(model.WithContext(r.ctx)).Table("channel_statistics cs").
		LeftJoin("channel c", "cs.channel_id = c.id").
		Where("cs.deleted_at IS NULL")

	// 日期筛选
	if date, ok := params["date"]; ok && date != "" {
		dateStr := date.(string)
		query = query.Where("DATE(cs.created_at)", "=", dateStr)
	}

	return query
}

// DisbursementStatistics 放款统计结构体
type DisbursementStatistics struct {
	DisbursementAmount        float64 `json:"disbursement_amount"`         // 放款金额
	DisbursementCustomerCount int     `json:"disbursement_customer_count"` // 放款客户数
	DisbursementOrderCount    int     `json:"disbursement_order_count"`    // 放款订单数
}

// DueStatistics 到期统计结构体
type DueStatistics struct {
	DueAmount          float64 `json:"due_amount"`           // 到期金额
	DueRepaymentAmount float64 `json:"due_repayment_amount"` // 到期回款总额
	DuePrincipalAmount float64 `json:"due_principal_amount"` // 到期本金总额
}

// OverdueStatistics 逾期统计结构体
type OverdueStatistics struct {
	OverdueCustomerCount int     `json:"overdue_customer_count"` // 逾期客户数
	OverdueAmount        float64 `json:"overdue_amount"`         // 逾期总额
}

// TrendDataPoint 趋势数据点结构
type TrendDataPoint struct {
	Date               string  `json:"date"`                // 日期 YYYY-MM-DD
	DisbursementAmount float64 `json:"disbursement_amount"` // 放款金额
	RepaymentAmount    float64 `json:"repayment_amount"`    // 回款金额
	DueAmount          float64 `json:"due_amount"`          // 到期金额
	RepaymentRate      float64 `json:"repayment_rate"`      // 回款率
	RegistrationCount  int     `json:"registration_count"`  // 注册量
}

// GetDisbursementStatistics 获取放款统计数据
func (r *Repository) GetDisbursementStatistics(date string) (*DisbursementStatistics, error) {
	query := model.DB(model.WithContext(r.ctx)).Table("business_loan_orders")

	if date == "" {
		// 累计数据：所有已放款的订单
		query = query.WhereIn("status", []interface{}{model.OrderStatusDisbursed, model.OrderStatusCompleted})
	} else {
		// 特定日期：该日期放款的订单
		query = query.WhereIn("status", []interface{}{model.OrderStatusDisbursed, model.OrderStatusCompleted}).
			Where("DATE(disbursed_at)", "=", date)
	}

	// 查询放款金额和订单数
	amountResult, err := query.Fields("COALESCE(SUM(loan_amount), 0) as total_amount, COUNT(*) as order_count").First()
	if err != nil {
		return nil, fmt.Errorf("查询放款金额和订单数失败: %v", err)
	}

	// 查询放款客户数
	customerQuery := query.Fields("COUNT(DISTINCT user_id) as customer_count")
	customerResult, err := customerQuery.First()
	if err != nil {
		return nil, fmt.Errorf("查询放款客户数失败: %v", err)
	}

	return &DisbursementStatistics{
		DisbursementAmount:        gconv.Float64(amountResult["total_amount"]),
		DisbursementOrderCount:    gconv.Int(amountResult["order_count"]),
		DisbursementCustomerCount: gconv.Int(customerResult["customer_count"]),
	}, nil
}

// GetDueStatistics 获取到期统计数据
func (r *Repository) GetDueStatistics(date string) (*DueStatistics, error) {
	query := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills")

	if date == "" {
		// 累计数据：所有到期日期 <= 今天的账单
		today := carbon.Now().Format("Y-m-d")
		query = query.Where("DATE(due_date)", "<=", today)
	} else {
		// 特定日期：该日期到期的账单
		query = query.Where("DATE(due_date)", "=", date)
	}

	// 查询到期金额和本金总额
	// 到期金额不包含减免金额
	totalResult, err := query.Fields(
		"COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount",
		"COALESCE(SUM(due_principal), 0) as due_principal_amount",
	).First()
	if err != nil {
		return nil, fmt.Errorf("查询到期金额失败: %v", err)
	}

	// 查询到期回款总额
	repaymentResult, err := query.Fields("COALESCE(SUM(paid_amount), 0) as repayment_amount").First()
	if err != nil {
		return nil, fmt.Errorf("查询到期回款总额失败: %v", err)
	}

	return &DueStatistics{
		DueAmount:          gconv.Float64(totalResult["due_amount"]),
		DuePrincipalAmount: gconv.Float64(totalResult["due_principal_amount"]),
		DueRepaymentAmount: gconv.Float64(repaymentResult["repayment_amount"]),
	}, nil
}

// GetOverdueStatistics 获取逾期统计数据
func (r *Repository) GetOverdueStatistics(date string) (*OverdueStatistics, error) {
	var cutoffDate string

	if date == "" {
		// 累计数据：截至今天
		cutoffDate = carbon.Now().Format("Y-m-d")
	} else {
		// 特定日期：截至该日期
		cutoffDate = date
	}

	// 查询逾期账单：到期日期 < 截止日期 且 状态为逾期相关
	overdueQuery := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills").
		Where("DATE(due_date)", "<", cutoffDate).
		WhereIn("status", []interface{}{
			model.RepaymentBillStatusOverdueUnpaid,
			model.RepaymentBillStatusOverduePartialPaid,
			model.RepaymentBillStatusOverduePaid,
		})

	// 查询逾期客户数
	customerResult, err := overdueQuery.Fields("COUNT(DISTINCT user_id) as customer_count").First()
	if err != nil {
		return nil, fmt.Errorf("查询逾期客户数失败: %v", err)
	}

	// 查询逾期总额：(total_due_amount - paid_amount - total_waive_amount)
	amountResult, err := overdueQuery.Fields(
		"COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount",
	).First()
	if err != nil {
		return nil, fmt.Errorf("查询逾期总额失败: %v", err)
	}

	return &OverdueStatistics{
		OverdueCustomerCount: gconv.Int(customerResult["customer_count"]),
		OverdueAmount:        gconv.Float64(amountResult["overdue_amount"]),
	}, nil
}

// GetDisbursementTrendData 获取放款金额趋势数据
func (r *Repository) GetDisbursementTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的放款金额趋势
	sql := `
		SELECT
			DATE(disbursed_at) as date,
			COALESCE(SUM(loan_amount), 0) as disbursement_amount
		FROM business_loan_orders
		WHERE status IN (?, ?)
			AND disbursed_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND disbursed_at < CURDATE()
		GROUP BY DATE(disbursed_at)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql,
		model.OrderStatusDisbursed, model.OrderStatusCompleted, days)
	if err != nil {
		return nil, fmt.Errorf("查询放款金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["disbursement_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:               date,
			DisbursementAmount: amount,
		})
	}

	return trendData, nil
}

// GetRepaymentTrendData 获取回款金额趋势数据
// 通过流水表查询还款相关的成功流水
func (r *Repository) GetRepaymentTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的回款金额趋势
	// 查询流水表中还款相关且状态为成功的流水记录
	sql := `
		SELECT
			DATE(completed_at) as date,
			COALESCE(SUM(amount), 0) as repayment_amount
		FROM business_payment_transactions
		WHERE type IN (?, ?, ?, ?)
			AND status = ?
			AND completed_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND completed_at < CURDATE()
			AND deleted_at IS NULL
		GROUP BY DATE(completed_at)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql,
		model.TransactionTypeRepayment,               // REPAYMENT-用户主动还款
		model.TransactionTypeWithhold,                // WITHHOLD-系统代扣
		model.TransactionTypeManualWithhold,          // MANUAL_WITHHOLD-管理员手动代扣
		model.TransactionTypePartialOfflineRepayment, // PARTIAL_OFFLINE_REPAYMENT-线下部分还款
		model.TransactionStatusSuccess,               // 2-处理成功
		days)
	if err != nil {
		return nil, fmt.Errorf("查询回款金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["repayment_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:            date,
			RepaymentAmount: amount,
		})
	}

	return trendData, nil
}

// GetDueAmountTrendData 获取到期金额趋势数据
// 查询账单表按到期日期统计应还总额
func (r *Repository) GetDueAmountTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的到期金额趋势
	sql := `
		SELECT
			DATE(due_date) as date,
			COALESCE(SUM(total_due_amount), 0) as due_amount
		FROM business_repayment_bills
		WHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
			AND due_date < CURDATE()
		GROUP BY DATE(due_date)
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql, days)
	if err != nil {
		return nil, fmt.Errorf("查询到期金额趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		amount := gconv.Float64(row["due_amount"])

		trendData = append(trendData, TrendDataPoint{
			Date:      date,
			DueAmount: amount,
		})
	}

	return trendData, nil
}

// GetRegistrationTrendData 获取注册量趋势数据
func (r *Repository) GetRegistrationTrendData(days int) ([]TrendDataPoint, error) {
	// 构建SQL查询，获取近N日的注册量趋势
	sql := `
		SELECT
			DATE(FROM_UNIXTIME(createtime)) as date,
			COUNT(*) as registration_count
		FROM business_app_account
		WHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))
			AND createtime < UNIX_TIMESTAMP(CURDATE())
		GROUP BY DATE(FROM_UNIXTIME(createtime))
		ORDER BY date
	`

	result, err := model.DB(model.WithContext(r.ctx)).Query(sql, days)
	if err != nil {
		return nil, fmt.Errorf("查询注册量趋势数据失败: %v", err)
	}

	var trendData []TrendDataPoint
	for _, row := range result {
		date := carbon.Parse(gconv.String(row["date"])).Format("Y-m-d")
		count := gconv.Int(row["registration_count"])

		trendData = append(trendData, TrendDataPoint{
			Date:              date,
			RegistrationCount: count,
		})
	}

	return trendData, nil
}
