package statistics

import "fincore/utils/jsonschema"

// GetChannelStatisticsSchema 渠道统计列表查询参数验证规则
func GetChannelStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "渠道统计列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{},
	}
}

// GetHomeStatisticsSchema 首页数据统计查询参数验证规则
func GetHomeStatistics() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "首页数据统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
		},
		Required: []string{},
	}
}

// GetTrendStatisticsSchema 趋势统计查询参数验证规则
func GetTrendStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "趋势统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"days": {
				Type:        "number",
				Required:    true,
				Enum:        []string{"7", "30"},
				Description: "查询天数，支持7日或30日",
			},
		},
		Required: []string{"days"},
	}
}
